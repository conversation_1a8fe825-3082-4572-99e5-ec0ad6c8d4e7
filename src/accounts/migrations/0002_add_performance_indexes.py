# Generated manually for performance optimization
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("accounts", "0001_initial"),
    ]

    operations = [
        # CustomUser model indexes
        migrations.AddIndex(
            model_name="customuser",
            index=models.Index(fields=["phone"], name="customuser_phone_idx"),
        ),
        migrations.AddIndex(
            model_name="customuser",
            index=models.Index(
                fields=["phone_verified"], name="customuser_phone_verified_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="customuser",
            index=models.Index(fields=["deleted_at"], name="customuser_deleted_at_idx"),
        ),
    ]
