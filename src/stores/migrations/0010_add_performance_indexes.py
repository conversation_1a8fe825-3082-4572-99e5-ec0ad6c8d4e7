# Generated manually for performance optimization
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("stores", "0009_activeorder"),
    ]

    operations = [
        # Store model indexes
        migrations.AddIndex(
            model_name="store",
            index=models.Index(
                fields=["owner", "deleted_at"], name="store_owner_deleted_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="store",
            index=models.Index(fields=["city"], name="store_city_idx"),
        ),
        migrations.AddIndex(
            model_name="store",
            index=models.Index(fields=["state"], name="store_state_idx"),
        ),
        migrations.AddIndex(
            model_name="store",
            index=models.Index(fields=["country"], name="store_country_idx"),
        ),
        
        # Order model indexes
        migrations.AddIndex(
            model_name="order",
            index=models.Index(
                fields=["store", "deleted_at"], name="order_store_deleted_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="order",
            index=models.Index(
                fields=["wholesaler", "deleted_at"], name="order_wholesaler_deleted_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="order",
            index=models.Index(
                fields=["status", "deleted_at"], name="order_status_deleted_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="order",
            index=models.Index(fields=["created_at"], name="order_created_at_idx"),
        ),
        migrations.AddIndex(
            model_name="order",
            index=models.Index(
                fields=["wholesaler", "status", "deleted_at"],
                name="order_wholesaler_status_deleted_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="order",
            index=models.Index(
                fields=["store", "status", "deleted_at"],
                name="order_store_status_deleted_idx",
            ),
        ),
        
        # OrderItem model indexes
        migrations.AddIndex(
            model_name="orderitem",
            index=models.Index(fields=["order"], name="orderitem_order_idx"),
        ),
        migrations.AddIndex(
            model_name="orderitem",
            index=models.Index(fields=["product_item"], name="orderitem_product_item_idx"),
        ),
        migrations.AddIndex(
            model_name="orderitem",
            index=models.Index(
                fields=["product_item", "order"], name="orderitem_product_order_idx"
            ),
        ),
    ]
