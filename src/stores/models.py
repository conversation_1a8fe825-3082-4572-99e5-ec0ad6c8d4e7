from datetime import timezone
from django.db import models

from accounts.models import CustomUser
from products.models import Region, Product
from wholesalers.models import Item, Wholesaler


class Store(models.Model):
    owner = models.OneToOneField(
        CustomUser, on_delete=models.CASCADE, related_name="store"
    )
    name = models.CharField(max_length=255)
    description = models.TextField()
    address = models.TextField()
    city = models.ForeignKey(
        Region,
        on_delete=models.SET_NULL,
        related_name="stores_city",
        null=True,
    )
    state = models.ForeignKey(
        Region,
        on_delete=models.SET_NULL,
        related_name="stores_state",
        null=True,
    )
    country = models.ForeignKey(
        Region,
        on_delete=models.SET_NULL,
        related_name="stores_country",
        null=True,
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    deleted_at = models.DateTimeField(null=True, blank=True, db_index=True)

    class Meta:
        verbose_name = "Store"
        verbose_name_plural = "Stores"
        indexes = [
            models.Index(
                fields=["owner", "deleted_at"], name="store_owner_deleted_idx"
            ),
            models.Index(fields=["city"], name="store_city_idx"),
            models.Index(fields=["state"], name="store_state_idx"),
            models.Index(fields=["country"], name="store_country_idx"),
        ]

    def __str__(self):
        return self.name

    def delete(self, *args, **kwargs):
        self.deleted_at = timezone.now()
        self.save()

    def hard_delete(self, *args, **kwargs):
        super(Store, self).delete(*args, **kwargs)


class OrderStatus(models.TextChoices):
    PENDING = "pending"
    PROCESSING = "processing"
    SHIPPED = "shipped"
    DELIVERED = "delivered"
    CANCELLED = "cancelled"


class Order(models.Model):
    wholesaler = models.ForeignKey(
        Wholesaler, on_delete=models.CASCADE, related_name="orders_wholesaler"
    )
    store = models.ForeignKey(Store, on_delete=models.CASCADE, related_name="orders")

    total_price = models.DecimalField(max_digits=10, decimal_places=2)
    fees = models.DecimalField(max_digits=10, decimal_places=2)

    deliver_at = models.DateTimeField(null=True, blank=True)

    products_total_price = models.DecimalField(max_digits=10, decimal_places=2)
    products_total_quantity = models.IntegerField()

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    deleted_at = models.DateTimeField(null=True, blank=True, db_index=True)

    final_completed_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
    )

    completed_at = models.DateTimeField(null=True, blank=True)

    status = models.CharField(
        max_length=255, choices=OrderStatus.choices, default=OrderStatus.PENDING
    )
    status_reason = models.TextField(null=True, blank=True)
    status_updated_at = models.DateTimeField(auto_now=True)
    status_updated_by = models.ForeignKey(
        CustomUser,
        on_delete=models.CASCADE,
        related_name="orders_status_updated_by",
        null=True,
        blank=True,
    )

    class Meta:
        verbose_name = "Order"
        verbose_name_plural = "Orders"
        indexes = [
            models.Index(
                fields=["store", "deleted_at"], name="order_store_deleted_idx"
            ),
            models.Index(
                fields=["wholesaler", "deleted_at"], name="order_wholesaler_deleted_idx"
            ),
            models.Index(
                fields=["status", "deleted_at"], name="order_status_deleted_idx"
            ),
            models.Index(fields=["created_at"], name="order_created_at_idx"),
            models.Index(
                fields=["wholesaler", "status", "deleted_at"],
                name="order_wholesaler_status_deleted_idx",
            ),
            models.Index(
                fields=["store", "status", "deleted_at"],
                name="order_store_status_deleted_idx",
            ),
        ]

    def __str__(self):
        return f"Order {self.id} for {self.store.name}"

    def delete(self, *args, **kwargs):
        self.deleted_at = timezone.now()
        self.save()

    def hard_delete(self, *args, **kwargs):
        super(Order, self).delete(*args, **kwargs)


class OrderItem(models.Model):
    order = models.ForeignKey(
        Order, on_delete=models.CASCADE, related_name="order_items"
    )
    product_item = models.ForeignKey(
        Item, on_delete=models.CASCADE, related_name="order_items"
    )
    quantity = models.IntegerField()
    price_per_unit = models.DecimalField(max_digits=10, decimal_places=2)
    total_price = models.DecimalField(max_digits=10, decimal_places=2)

    class Meta:
        verbose_name = "Order Item"
        verbose_name_plural = "Order Items"
        indexes = [
            models.Index(fields=["order"], name="orderitem_order_idx"),
            models.Index(fields=["product_item"], name="orderitem_product_item_idx"),
            models.Index(
                fields=["product_item", "order"], name="orderitem_product_order_idx"
            ),
        ]

    def __str__(self):
        return f"Order Item {self.id} for {self.product_item.product.title}"
