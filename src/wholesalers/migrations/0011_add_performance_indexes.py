# Generated manually for performance optimization
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("wholesalers", "0010_item_maximum_order_quantity_and_more"),
    ]

    operations = [
        # Wholesaler model indexes
        migrations.AddIndex(
            model_name="wholesaler",
            index=models.Index(fields=["deleted_at"], name="wholesaler_deleted_at_idx"),
        ),
        migrations.AddIndex(
            model_name="wholesaler",
            index=models.Index(
                fields=["user", "deleted_at"], name="wholesaler_user_deleted_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="wholesaler",
            index=models.Index(
                fields=["category", "deleted_at"],
                name="wholesaler_category_deleted_idx",
            ),
        ),
        
        # RegionMinCharge model indexes
        migrations.AddIndex(
            model_name="regionmincharge",
            index=models.Index(fields=["deleted_at"], name="regionmincharge_deleted_at_idx"),
        ),
        migrations.AddIndex(
            model_name="regionmincharge",
            index=models.Index(
                fields=["wholesaler", "region"],
                name="regionmincharge_wholesaler_region_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="regionmincharge",
            index=models.Index(
                fields=["region", "deleted_at"],
                name="regionmincharge_region_deleted_idx",
            ),
        ),
        
        # Item model indexes
        migrations.AddIndex(
            model_name="item",
            index=models.Index(fields=["deleted_at"], name="item_deleted_at_idx"),
        ),
        migrations.AddIndex(
            model_name="item",
            index=models.Index(
                fields=["wholesaler", "deleted_at"], name="item_wholesaler_deleted_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="item",
            index=models.Index(
                fields=["product", "deleted_at"], name="item_product_deleted_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="item",
            index=models.Index(fields=["inventory_count"], name="item_inventory_count_idx"),
        ),
        migrations.AddIndex(
            model_name="item",
            index=models.Index(
                fields=["wholesaler", "product"], name="item_wholesaler_product_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="item",
            index=models.Index(
                fields=["inventory_count", "deleted_at"],
                name="item_inventory_deleted_idx",
            ),
        ),
    ]
